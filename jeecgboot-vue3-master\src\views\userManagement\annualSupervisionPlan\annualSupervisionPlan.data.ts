import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '人员名称',
    dataIndex: 'userName',
    resizable: true,
  },
  {
    title: '状态',
    dataIndex: 'auditStatus',
    resizable: true,
    customRender: ({ text }) => {
      if (text == '0') {
        return '填写完成';
      } else if (text == '1') {
        return '审批完成';
      } else if (text == '2') {
        return '二次审批完成';
      } else if (text == '99') {
        return '驳回';
      } else {
        return text;
      }
    },
  },
  {
    title: '企业微信审批状态',
    dataIndex: 'spStatus',
    resizable: true,
    customRender: ({ text }) => {
      if (text == '1') {
        return '审批中';
      } else if (text == '2') {
        return '已通过';
      } else if (text == '3') {
        return '已驳回';
      } else if (text == '4') {
        return '已撤销';
      } else if (text == '6') {
        return '通过后撤销';
      } else if (text == '7') {
        return '已删除';
      } else if (text == '10') {
        return '已支付';
      } else {
        return text;
      }
    },
  },
  {
    title: '审批单号',
    dataIndex: 'spNo',
    resizable: true,
  },
  {
    title: '培训名称',
    dataIndex: 'className',
    resizable: true,
  },
  {
    title: '外派培训原因',
    dataIndex: 'trainReason',
    resizable: true,
  },
  {
    title: '培训目的',
    dataIndex: 'trainPurpose',
    resizable: true,
  },
  {
    title: '培训地点',
    dataIndex: 'trainPlace',
    resizable: true,
  },
  {
    title: '举办单位',
    dataIndex: 'organizingUnit',
    resizable: true,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    resizable: true,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    resizable: true,
  },
  {
    title: '实际课时总数',
    dataIndex: 'trainNumber',
    resizable: true,
  },
  {
    title: '预算费用',
    dataIndex: 'estimatedCost',
    resizable: true,
  },
  {
    title: '实际费用',
    dataIndex: 'realCost',
    resizable: true,
  },
  {
    title: '有无相关证书',
    dataIndex: 'relatedCertificates',
    resizable: true,
  },
  {
    title: '部门所属体系',
    dataIndex: 'departmentSystem',
    resizable: true,
  },
  // {
  //   title: '预算费用明细',
  //   dataIndex: 'estimatedCostUrl',
  //   resizable: true,
  // },
  // {
  //   title: '费用明细',
  //   dataIndex: 'costDetailUrl',
  //   resizable: true,
  // },
  // {
  //   title: '培训课程内容记录',
  //   dataIndex: 'recordUrl',
  //   resizable: true,
  // },
  // {
  //   title: '培训心得体会',
  //   dataIndex: 'experienceUrl',
  //   resizable: true,
  // },
  // {
  //   title: '授课老师评估/ 培训服务机构评估',
  //   dataIndex: 'assessmentUrl',
  //   resizable: true,
  // },

  // {
  //   title: '创建人',
  //   dataIndex: 'creator_dictText'
  // },
  // {
  //   title: '创建时间',
  //   dataIndex: 'createTime'
  // },
];

export const searchFormSchema: FormSchema[] = [
   {
    label: '时间',
    field: 'timeRange',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      valueType: 'Date',
    },
  },

];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: '人员工号',
    field: 'userCode',
    component: 'Input',
    dynamicDisabled: true,
    required: true,
  },
  {
    label: '人员名称',
    field: 'userName',
    component: 'Input',
    dynamicDisabled: true,
    required: true,
  },
  {
    label: '培训班名称',
    field: 'className',
    component: 'Input',
    required: true,
  },
  {
    label: '外派培训原因',
    field: 'trainReason',
    component: 'Input',
    required: true,
  },
  {
    label: '培训目的',
    field: 'trainPurpose',
    component: 'Input',
  },
  {
    label: '培训地点',
    field: 'trainPlace',
    component: 'Input',
    required: true,
  },
  {
    label: '举办单位',
    field: 'organizingUnit',
    component: 'Input',
    required: true,
  },
  {
    label: '实际课时总数',
    field: 'trainNumber',
    component: 'Input',
    required: true,
  },
  {
    label: '预算费用',
    field: 'estimatedCost',
    component: 'Input',
    required: true,
  },
  {
    label: '实际费用',
    field: 'realCost',
    component: 'Input',
    required: true,
  },
  {
    label: '有无相关证书',
    field: 'relatedCertificates',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '有', value: '有' },
        { label: '无', value: '无' },
      ],
    },
    required: true,
  },
  {
    label: '部门所属体系',
    field: 'departmentSystem',
    component: 'Select',
    componentProps: {
      options: [
        { label: '供应链体系', value: '供应链体系' },
        { label: '产品开发中心', value: '产品开发中心' },
        { label: 'ODM业务部', value: 'ODM业务部' },
        { label: '日化事业部', value: '日化事业部' },
        { label: '电商生态事业部', value: '电商生态事业部' },
        { label: '其他部门', value: '其他部门' },
      ],
    },
    required: true,
  },
  {
    label: '起止时间',
    field: 'duration',
    component: 'RangePicker',
    required: true,
  },
  {
    label: '培训内容',
    field: 'content',
    component: 'InputTextArea',
    required: true,
  },
  {
    label: '预算费用明细',
    field: 'estimatedCostUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
  {
    label: '费用明细',
    field: 'costDetailUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
  {
    label: '培训课程内容记录',
    field: 'recordUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
  {
    label: '培训心得体会',
    field: 'experienceUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
  {
    label: '授课老师评估/ 培训服务机构评估',
    field: 'assessmentUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
];
