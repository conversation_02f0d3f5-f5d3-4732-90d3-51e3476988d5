# Project Structure

## Root Directory Structure

```
├── src/                    # Source code
├── types/                  # TypeScript type definitions
├── build/                  # Build configuration and scripts
├── mock/                   # Mock data for development
├── public/                 # Static assets
├── tests/                  # Test files
├── .kiro/                  # Kiro AI assistant configuration
├── .vscode/                # VS Code configuration
└── node_modules/           # Dependencies
```

## Source Code Organization (`src/`)

### Core Application Files
- `main.ts` - Application entry point and bootstrap
- `App.vue` - Root Vue component

### Feature Directories
- `api/` - API service layer and HTTP requests
- `components/` - Reusable Vue components
- `views/` - Page-level Vue components (routes)
- `layouts/` - Layout components for different page structures
- `router/` - Vue Router configuration and route definitions
- `store/` - Pinia state management stores

### Utilities & Configuration
- `utils/` - Utility functions and helpers
- `hooks/` - Vue 3 Composition API hooks
- `directives/` - Custom Vue directives
- `settings/` - Application configuration and settings
- `locales/` - Internationalization files
- `logics/` - Business logic and application initialization
- `enums/` - TypeScript enums and constants

### Styling & Assets
- `assets/` - Images, fonts, and other static assets
- `design/` - Global styles and design tokens

### Special Features
- `qiankun/` - Micro-frontend configuration (qiankun framework)

## Key Conventions

### File Naming
- **Vue Components**: PascalCase (e.g., `UserManagement.vue`)
- **TypeScript Files**: camelCase (e.g., `userService.ts`)
- **Directories**: camelCase (e.g., `userManagement/`)

### Component Organization
- **Views**: Organized by feature/module in `src/views/`
- **Components**: Shared components in `src/components/`
- **Modules**: Feature-specific components in `modules/` subdirectories

### Path Aliases
- `/@/` - Points to `src/` directory
- `/#/` - Points to `types/` directory

### API Structure
- API services organized by feature in `src/api/`
- Each feature has its own API module
- Consistent use of Axios for HTTP requests

### State Management
- Pinia stores organized by feature
- Store files typically named `useXxxStore.ts`

### Type Definitions (`types/`)
- `global.d.ts` - Global type definitions
- `config.d.ts` - Configuration types
- `store.d.ts` - Store-related types
- `utils.d.ts` - Utility types
- Feature-specific type files

### Build Configuration (`build/`)
- Vite plugins and configuration
- Build scripts and utilities
- Theme and styling configuration

### Mock Data (`mock/`)
- Development mock APIs
- Organized by feature/module
- Uses Vite mock plugin

## Typical Feature Structure

```
src/views/featureName/
├── index.vue              # Main feature page
├── modules/               # Feature-specific components
│   ├── FeatureModal.vue   # Modal components
│   └── FeatureForm.vue    # Form components
└── api.ts                 # Feature-specific API calls
```

## Import Conventions
- Use path aliases (`/@/` and `/#/`)
- Prefer named imports over default imports
- Group imports: external libraries, internal modules, types