<template>
    <BasicModal v-bind="$attrs" @register="registerModal" title="人员上岗能力确认记录" okText="关闭" @ok="handleSubmit"
        :width="1400">
        <div class="table-container">
            <div class="table-header">
                <div class="table-actions">
                    <button class="action-btn" @click="printTable">打印</button>
                </div>
            </div>
            <table :id="printId" border="1" cellspacing="0" cellpadding="5"
                style="width: 100%; border-collapse: collapse">
                <thead>
                    <tr>
                        <th style="text-align: center" colspan="6">人员上岗能力确认记录</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th style="width: 3rem;">姓名：</th>
                        <td style="width: 4rem;">{{ detailInfo.staffInfoBO?.staffName || '' }}</td>
                        <th style="width: 3rem;">性别：</th>
                        <td style="width: 3rem;">{{ detailInfo.staffInfoBO?.sex == 'M' ? '男' : (detailInfo.staffInfoBO?.sex == 'F' ? '女' : '') }}</td>
                        <th style="width: 3rem;">年龄：</th>
                        <td style="width: 3rem;">{{calculateAge(detailInfo.staffInfoBO?.birthday)}}</td>
                    </tr>
                    <tr>
                        <th rowspan="4">拟授权范围：</th>
                        <th>岗位：</th>
                        <td colspan="4">{{ detailInfo.staffInfoBO?.positioncode_dictText || '' }}</td>
                    </tr>
                    <tr>
                        <th>检测项目：</th>
                        <td colspan="4">{{ recordInfo.authorizedContent || '' }}</td>
                    </tr>
                    <tr>
                        <th>操作仪器：</th>
                        <td colspan="4">{{ recordInfo.instrumentContent || '' }}</td>
                    </tr>
                    <tr>
                        <th>其他授权：</th>
                        <td colspan="4">{{ recordInfo.otherAuthorization || '' }}</td>
                    </tr>
                    <tr>
                        <th rowspan="5">确认依据：</th>
                        <th>学历/专业：</th>
                        <td colspan="4">{{ detailInfo.educationBOS?.[0]?.school || '' }}-{{ detailInfo.educationBOS?.[0]?.education || '' }}</td>
                    </tr>
                    <tr>
                        <th>工作经历：</th>
                        <td colspan="4">{{ detailInfo.experienceBOS?.[0]?.bdate || '' }}-{{ detailInfo.experienceBOS?.[0]?.edate || '' }}-{{ detailInfo.experienceBOS?.[0]?.comp || '' }}-{{ detailInfo.experienceBOS?.[0]?.zhiwu || '' }}</td>
                    </tr>
                    <tr>
                        <th>技术职称/资质证书：</th>
                        <td colspan="4">{{ detailInfo.staffInfoBO?.technicalTitle || '' }}</td>
                    </tr>
                    <tr>
                        <th>培训/考核：</th>
                        <td colspan="4">{{ detailInfo.position || '' }}</td>
                    </tr>
                    <tr>
                        <th>其他方面：</th>
                        <td colspan="4">{{ recordInfo.otherContent || '' }}</td>
                    </tr>
                    <tr>
                        <th>确认结论：</th>
                        <td colspan="5">
                            {{ recordInfo.auditContent || '' }}
                            <br>
                            <a-row>
                                <a-col :span="12">技术负责人：{{ recordInfo.auditPerson_dictText || '' }}</a-col>
                                <a-col :span="12">日期:{{ recordInfo.auditTime || '' }}</a-col>
                            </a-row>

                        </td>
                    </tr>
                    <tr>
                        <th>实验室审批意见：</th>
                        <td colspan="5">
                            {{ recordInfo.assignContent || '' }}
                            <br>
                            <a-row>
                                <a-col :span="12">实验室经理：{{ recordInfo.assignPerson_dictText || '' }}</a-col>
                                <a-col :span="12">日期:{{ recordInfo.assignTime || '' }}</a-col>
                            </a-row>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';
import { defHttp1 } from '/@/utils/http/axios/index1';
const printId = ref('');
const detailInfo = ref<any>({});
const recordInfo = ref<any>({});
// 声明Emits
const emit = defineEmits(['success', 'register']);

//表单赋值
const [registerModal, { closeModal }] = useModalInner(async (data) => {
    try {
        // 生成打印ID
        printId.value = buildUUID().toString();

        // 接口请求前重置字段
        detailInfo.value = {};
        recordInfo.value = {};

        // 先赋值传入的记录信息
        recordInfo.value = data.record || {};

        // 发起接口请求获取详细信息
        const res = await defHttp1.get({
            url: `/lims/employee/getOnDutyAbility?userCode=${data.record.userCode}`
        });

        // 请求成功后赋值
        if (res && res.result) {
            detailInfo.value = res.result;
            console.log("🚀 ~ 获取人员详细信息成功:", detailInfo.value.staffInfoBO?.staffName);
        } else {
            console.warn("接口返回数据为空");
        }
    } catch (error) {
        console.error("获取人员详细信息失败:", error);
        // 请求失败时保持字段为空对象，避免显示错误数据
        detailInfo.value = {};
    }
});

// 打印表格
function printTable() {
    printJS({
        type: 'html',
        printable: printId.value,
        scanStyles: false,
    });
}
function calculateAge(birthday: string | null | undefined): number {
    try {
        if (!birthday || typeof birthday !== 'string') {
            return 0;
        }

        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(birthday)) {
            return 0;
        }

        const birthDate = new Date(birthday);
        if (isNaN(birthDate.getTime())) {
            return 0;
        }

        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();

        const monthDiff = today.getMonth() - birthDate.getMonth();
        const dayDiff = today.getDate() - birthDate.getDate();

        if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
            age--;
        }

        return Math.max(0, age);
    } catch (error) {
        console.error('计算年龄时出错:', error);
        return 0;
    }
}
async function handleSubmit() {
    closeModal();
}
</script>

<style scoped>
.table-container {
    padding: 20px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-title {
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 5px 10px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.action-btn:hover {
    background-color: #40a9ff;
}

.action-btn:disabled {
    background-color: #d9d9d9;
    cursor: not-allowed;
}

table {
    border: 1px solid #ccc;
    width: 100%;
}

th,
td {
    border: 1px solid #ccc;
    text-align: center;
    padding: 8px;
    height: 40px;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}
</style>
