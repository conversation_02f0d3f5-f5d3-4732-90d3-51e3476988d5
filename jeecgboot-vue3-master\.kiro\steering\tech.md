# Technology Stack

## Core Framework
- **Vue 3.2.33** - Main frontend framework using Composition API
- **TypeScript 4.6.3** - Primary development language
- **Vite 3.0.2** - Build tool and dev server
- **Ant Design Vue 3.2.12** - UI component library

## State Management & Routing
- **Pinia 2.0.12** - State management (Vuex replacement)
- **Vue Router 4.3.0** - Client-side routing
- **Vue I18n 9.1.9** - Internationalization

## Styling & CSS
- **WindiCSS** - Utility-first CSS framework
- **Less 4.1.2** - CSS preprocessor
- **PostCSS** - CSS post-processing

## Development Tools
- **ESLint** - Code linting with Vue 3 and TypeScript rules
- **Prettier** - Code formatting
- **Stylelint** - CSS/Less linting
- **Husky** - Git hooks
- **Jest** - Unit testing framework

## Key Libraries
- **Axios 0.26.1** - HTTP client
- **Dayjs 1.11.7** - Date manipulation
- **Lodash-es 4.17.21** - Utility functions
- **VXE Table 4.1.0** - Advanced data table component
- **ECharts 5.3.2** - Data visualization
- **Crypto-js 4.1.1** - Cryptographic functions

## Package Manager
- **PNPM** - Preferred package manager (faster than npm/yarn)

## Common Commands

### Development
```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev
# or
pnpm serve

# Type checking
pnpm type:check
```

### Building
```bash
# Production build
pnpm build

# Test environment build
pnpm build:test

# Build with bundle analyzer
pnpm report

# Preview production build
pnpm preview
```

### Code Quality
```bash
# Lint and fix JavaScript/TypeScript/Vue files
pnpm lint:eslint

# Format code with Prettier
pnpm lint:prettier

# Lint and fix CSS/Less files
pnpm lint:stylelint

# Run all linting
pnpm lint:lint-staged
```

### Testing
```bash
# Run unit tests
pnpm test:unit

# Run tests with coverage
pnpm test:unit-coverage
```

### Maintenance
```bash
# Clean cache
pnpm clean:cache

# Clean node_modules
pnpm clean:lib

# Reinstall all dependencies
pnpm reinstall
```

## Environment Configuration
- Development: `.env.development`
- Production: `.env.production`
- Test: `.env.test`

## Build Configuration
- **Vite Config**: `vite.config.ts`
- **TypeScript Config**: `tsconfig.json`
- **WindiCSS Config**: `windi.config.ts`