<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form ref="formRef" :model="orderMainModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">

      <a-row class="form-row" :gutter="8">
        <a-col :span="20" v-show="false">
          <a-form-item label="人员工号" name="userCode">
            <a-input disabled v-model:value="orderMainModel.userCode" placeholder="请输入人员工号" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="人员名称" name="userName">
            <a-input :class="{ fontColor: true }" disabled v-model:value="orderMainModel.userName"
              placeholder="请输入人员名称" />
          </a-form-item>
        </a-col>
        <a-col :span="2">
          <a-button preIcon="ant-design:user-switch-outlined" @click="addPerson">添加人员</a-button>
        </a-col>
        <a-col :span="20">
          <a-form-item label="其他授权" name="otherAuthorization">
            <a-input v-model:value="orderMainModel.otherAuthorization" placeholder="请输入其他授权" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="培训考核" name="outTrainingContent">
            <a-input v-model:value="orderMainModel.outTrainingContent" placeholder="请输入培训考核" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="其他方面" name="otherContent">
            <a-input v-model:value="orderMainModel.otherContent" placeholder="请输入其他方面" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <ChoosePersonModal @register="cpModal" @success="handleCpReturn"></ChoosePersonModal>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { saveOrUpdate } from '../capabilityConfirm.api';
import ChoosePersonModal from '/@/views/reagent/modules/ChoosePersonModal.vue';
// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref();
const isUpdate = ref(true);
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});
const orderMainModel = reactive({
  id: null,
  otherAuthorization: null,
  outTrainingContent: null,
  otherContent: null,
  userCode: '',
  userName: '',
  userCode: '',
  userName: '',
});
const validatorRules = {
  userCode: [
    { required: true, message: '人员必选' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  userName: [
    { required: true, message: '人员必选' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  otherAuthorization: [
    { required: false, message: '其他授权必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  outTrainingContent: [
    { required: false, message: '培训考核必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  otherContent: [
    { required: false, message: '其他方面必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
};
//表单配置
// const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
//     labelWidth: 150,
//     schemas: formSchema,
//     showActionButtonGroup: false,
// });
//表单赋值
const [cpModal, { openModal: choosePModal }] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  // await resetFields();
  // setTimeout(()=>{
  formRef.value.resetFields();
  // },300)
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {
    //表单赋值
    Object.assign(orderMainModel, data.record);
  }
  console.log('🚀 ~ file:  ~ orderMainModel:', orderMainModel);
});
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('error', error);
    });
}
function reset() {
  orderMainModel.id = null;
  orderMainModel.otherAuthorization = null;
  orderMainModel.outTrainingContent = null;
  orderMainModel.otherContent = null;
  orderMainModel.userCode = '';
  orderMainModel.userName = '';
}
function addPerson() {
  choosePModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}

function handleCpReturn(source: any) {
  console.log("🚀 ~ file: PlanDevelopmentModal.vue:163 ~ handleCpReturn ~ source:", source)

  if (source.length != 0) {
    orderMainModel.userCode = '';
    orderMainModel.userName = '';

    for (let i = 0; i < source.length; i++) {
      orderMainModel.userCode += source[i].username;
      orderMainModel.userName += source[i].realname;

      if (i + 1 != source.length) {
        orderMainModel.userCode += ',';
        orderMainModel.userName += ',';
      }
    }
  }

}
</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>