# Product Overview

JeecgBoot-Vue3 is a low-code development platform frontend built with Vue 3. It's part of the JeecgBoot ecosystem, providing a comprehensive admin interface with code generation capabilities.

## Key Features

- **Low-code Development**: GUI code generator that creates front-end and back-end code
- **Enterprise Admin System**: Complete user management, role management, permissions, and system monitoring
- **Online Development**: Online forms, reports, and code generation tools
- **Multi-tenant Support**: Built-in multi-tenancy capabilities
- **Microservice Ready**: Supports both monolithic and microservice architectures
- **Rich Components**: Extensive component library including tables, forms, charts, and business components

## Target Users

- Enterprise developers building admin systems
- Teams needing rapid application development
- Organizations requiring comprehensive permission management
- Projects needing code generation and low-code capabilities

## Business Domain

This is a general-purpose enterprise admin platform suitable for various business domains including but not limited to:
- User management systems
- Content management
- Business process management
- Data visualization and reporting
- System administration tools